<?php
use app\common\models\PaymentType;
use app\modules\backend\models\WorkerFinances;
use yii\helpers\Html;
use app\assets\Select2Asset;

Select2Asset::register($this);
?>

<style>
.payment-methods-container {
    padding: 8px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.payment-methods-container .form-check {
    margin-bottom: 0;
    padding: 8px 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #dee2e6;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    min-width: 120px;
    position: relative;
}

.payment-methods-container .form-check:hover {
    border-color: #007bff;
    box-shadow: 0 1px 3px rgba(0,123,255,0.1);
}

.payment-methods-container .form-check-input {
    margin: 0;
    transform: scale(1.1);
    flex-shrink: 0;
    position: absolute;
    right: 10px;
}

.payment-methods-container .form-check-label {
    font-weight: 500;
    font-size: 14px;
    color: #495057;
    cursor: pointer;
    margin: 0;
    white-space: nowrap;
    text-align: left;
    padding-right: 24px;
}

.payment-methods-container .form-check-input:disabled + .form-check-label {
    opacity: 0.5;
    cursor: not-allowed;
}

.payment-methods-container .form-check-input:checked + .form-check-label {
    color: #007bff;
    font-weight: 600;
}

/* Стили для основных чекбоксов типов платежей */
.payment-type-row .form-check {
    padding: 12px 15px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    min-width: 150px;
    position: relative;
}

.payment-type-row .form-check:hover {
    background: #e3f2fd;
    border-color: #2196f3;
}

.payment-type-row .form-check-input {
    margin: 0;
    transform: scale(1.2);
    flex-shrink: 0;
    position: absolute;
    right: 10px;
}

.payment-type-row .form-check-label {
    font-weight: 600;
    font-size: 15px;
    color: #343a40;
    margin: 0;
    white-space: nowrap;
    text-align: left;
    padding-right: 24px;
}

.payment-type-row .form-check-input:checked + .form-check-label {
    color:hsl(207, 89.70%, 54.10%);
}

/* Выравнивание контента в ячейках таблицы */
.payment-type-row td {
    vertical-align: middle;
}

.amount-column {
    vertical-align: middle;
}

/* Стили для полей ввода чисел */
input[inputmode="numeric"] {
    text-align: right;
}

/* Стили для секции погашения долга */
#debt-payment-section .card {
    border: 1px solid #dee2e6;
    border-radius: 6px;
    background: white;
}

#debt-payment-section .card-header {
    background:rgb(160, 190, 239);
    border-bottom: 1px solid #dee2e6;
    padding: 12px 15px;
}

#debt-payment-section .form-check {
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    position: relative;
    cursor: pointer;
}

#debt-payment-section .form-check:hover {
    background: #f8f9fa;
    border-radius: 4px;
    padding: 8px 12px;
    margin: -8px -12px;
}

#debt-payment-section .form-check-input {
    margin: 0;
    transform: scale(1.2);
    flex-shrink: 0;
    position: absolute;
    right: 10px;
}

#debt-payment-section .form-check-label {
    font-weight: 350;
    font-size: 15px;
    color:rgb(2, 11, 20);
    margin: 0;
    cursor: pointer;
    padding-right: 30px;
    width: 100%;
}

#debt-payment-section .form-check-input:checked + .form-check-label {
    color: #dc3545;
}

#debt-payment-section .card-body {
    background: white;
    padding: 20px;
}

/* Стили для валидации */
.is-invalid {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}

.error-container {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.validation-success {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
}

.validation-warning {
    border-color: #ffc107 !important;
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25) !important;
}

/* Стили для панели итогов */
#payment-summary .card-header {
    background: linear-gradient(45deg, #17a2b8, #138496) !important;
}

#payment-summary .card-body {
    background: #f8f9fa;
}

/* Анимация для полей с ошибками */
.is-invalid {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Стили для кнопок */
.btn-lg {
    padding: 12px 30px;
    font-size: 16px;
    border-radius: 8px;
}

.btn-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border: none;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #0056b3, #004085);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.btn-secondary {
    background: linear-gradient(45deg, #6c757d, #545b62);
    border: none;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: linear-gradient(45deg, #545b62, #3d4142);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(108, 117, 125, 0.3);
}

/* Улучшенные стили для алертов */
.alert {
    border-radius: 8px;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.alert-success {
    background: linear-gradient(45deg, #d4edda, #c3e6cb);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(45deg, #f8d7da, #f5c6cb);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(45deg, #fff3cd, #ffeaa7);
    color: #856404;
}
</style>

<div class="worker-payment-form">
    <form id="worker-payment-create-form">
        
        <div class="row mb-3">
            <div class="col-md-8">
                <label for="worker_id"><?= Yii::t('app', 'worker') ?></label>
                <select id="worker_id" name="worker_id" class="form-control select2" required>
                    <option value=""><?= Yii::t('app', 'select_worker') ?></option>
                    <?php foreach ($workers as $worker): ?>
                        <option value="<?= $worker['id'] ?>"><?= Html::encode($worker['full_name']) ?></option>
                    <?php endforeach; ?>
                </select>
                <div class="error-container" id="worker_id-error"></div>
            </div>
            <div class="col-md-4">
                <label for="month"><?= Yii::t('app', 'month') ?></label>
                <input type="month" id="month" name="month" class="form-control" value="<?= date('Y-m') ?>" required>
                <div class="error-container" id="month-error"></div>
            </div>
        </div>

        <!-- Worker Info Panel -->
        <div id="worker-info" class="alert alert-info mt-3" style="display: none;">
            <div class="row">
                <div class="col-md-4">
                    <strong><?= Yii::t('app', 'salary') ?>:</strong> <span id="worker-salary">0</span>
                </div>
                <div class="col-md-4">
                    <strong><?= Yii::t('app', 'total_paid') ?>:</strong> <span id="worker-total-paid">0</span>
                </div>
                <div class="col-md-4" id="remaining-salary-info" style="display: none;">
                    <strong><?= Yii::t('app', 'remaining_salary') ?>:</strong> <span id="worker-remaining-salary">0</span>
                </div>
            </div>
        </div>

        <!-- Payment Types Selection -->
        <div class="form-group">
            <label><?= Yii::t('app', 'payment_types') ?></label>
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th width="40%"><?= Yii::t('app', 'payment_type') ?></th>
                            <th width="30%"><?= Yii::t('app', 'payment_method') ?></th>
                            <th width="30%"><?= Yii::t('app', 'amount') ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($paymentTypes as $typeId => $typeName): ?>
                            <?php if ($typeId != WorkerFinances::TYPE_DEBT_PAYMENT): // Исключаем долг из основной таблицы ?>
                                <tr class="payment-type-row" data-type="<?= $typeId ?>">
                                    <td>
                                        <div class="form-check">
                                            <input class="form-check-input payment-type-checkbox" type="checkbox" 
                                                   id="payment_type_<?= $typeId ?>" value="<?= $typeId ?>">
                                            <label class="form-check-label" for="payment_type_<?= $typeId ?>">
                                                <?= $typeName ?>
                                            </label>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="payment-methods-container">
                                            <div class="form-check">
                                                <input class="form-check-input payment-method-checkbox" type="checkbox" 
                                                       id="cash_<?= $typeId ?>" value="<?= PaymentType::CASH ?>"
                                                       name="payment_types[<?= $typeId ?>][methods][]" disabled>
                                                <label class="form-check-label" for="cash_<?= $typeId ?>">
                                                    <?= Yii::t('app', 'cash') ?>
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input payment-method-checkbox" type="checkbox" 
                                                       id="card_<?= $typeId ?>" value="<?= PaymentType::PAYMENT_CARD ?>"
                                                       name="payment_types[<?= $typeId ?>][methods][]" disabled>
                                                <label class="form-check-label" for="card_<?= $typeId ?>">
                                                    <?= Yii::t('app', 'payment_card') ?>
                                                </label>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="amount-column">
                                        <input type="text"
                                               class="form-control amount-input"
                                               name="payment_types[<?= $typeId ?>][amount]"
                                               placeholder="<?= Yii::t('app', 'amount') ?>"
                                               inputmode="numeric"
                                               pattern="[0-9\s]*"
                                               disabled>
                                        <div class="dynamic-amounts" style="display: none;">
                                            <!-- Dynamic inputs will be added here -->
                                        </div>
                                        <div class="error-container"></div>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Debt Payment Section -->
        <div id="debt-payment-section" class="form-group" style="display: none;">
                <div class="card-header">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="debt-payment-checkbox">
                        <label class="form-check-label" for="debt-payment-checkbox">
                            <strong><?= $paymentTypes[WorkerFinances::TYPE_DEBT_PAYMENT] ?? 'Погашение долга' ?></strong>
                        </label>
                    </div>
                </div>
                <div class="card-body" id="debt-payment-details" style="display: none;">
                    <div class="row">
                        <div class="col-md-12">
                            <label><?= Yii::t('app', 'amount') ?></label>
                            <input type="text" class="form-control payment-amount" id="debt-payment-amount"
                                   name="payment_types[<?= WorkerFinances::TYPE_DEBT_PAYMENT ?>][amount]"
                                   inputmode="numeric" pattern="[0-9\s]*" disabled>
                            <small class="text-muted"><?= Yii::t('app', 'debt_amount') ?>: <span id="worker-debt-amount">0</span></small>
                            <div class="error-container"></div>
                        </div>
                    </div>
                </div>
        </div>

    </form>
</div>

<!-- JavaScript модули для формы создания платежа работнику -->
<script src="<?= Yii::getAlias('@web') ?>/modules/backend/views/worker-payment/js/validation.js"></script>
<script src="<?= Yii::getAlias('@web') ?>/modules/backend/views/worker-payment/js/calculations.js"></script>
<script src="<?= Yii::getAlias('@web') ?>/modules/backend/views/worker-payment/js/ui-helpers.js"></script>
<script src="<?= Yii::getAlias('@web') ?>/modules/backend/views/worker-payment/js/ajax-requests.js"></script>
<script src="<?= Yii::getAlias('@web') ?>/modules/backend/views/worker-payment/js/form-handlers.js"></script>
<script src="<?= Yii::getAlias('@web') ?>/modules/backend/views/worker-payment/js/main.js"></script>

<script>
// Инициализация приложения с константами из PHP
WorkerPaymentApp.ready({
    WORKER_FINANCES_TYPE_SALARY: <?= WorkerFinances::TYPE_SALARY ?>,
    WORKER_FINANCES_TYPE_ADVANCE: <?= WorkerFinances::TYPE_ADVANCE ?>,
    WORKER_FINANCES_TYPE_DEBT_PAYMENT: <?= WorkerFinances::TYPE_DEBT_PAYMENT ?>,
    PAYMENT_TYPE_CASH: <?= PaymentType::CASH ?>,
    PAYMENT_TYPE_TRANSFER: <?= PaymentType::TRANSFER ?>,
    PAYMENT_TYPE_TERMINAL: <?= PaymentType::TERMINAL ?>,
    PAYMENT_TYPE_PAYMENT_CARD: <?= PaymentType::PAYMENT_CARD ?>,
    CASH_LABEL: "<?= Yii::t('app', 'cash') ?>",
    PAYMENT_CARD_LABEL: "<?= Yii::t('app', 'payment_card') ?>"
});
    // Делаем весь контейнер кликабельным для чекбокса погашения долга
    $('#debt-payment-section .form-check').on('click', function(e) {
        // Проверяем, что клик был не на самом чекбоксе и не на лейбле (т.к. лейбл уже связан с чекбоксом)
        if (!$(e.target).is('input[type="checkbox"], label')) {
            var checkbox = $(this).find('input[type="checkbox"]');
            checkbox.prop('checked', !checkbox.prop('checked')).trigger('change');
        }
    });
    
    // Делаем весь контейнер кликабельным для чекбоксов типов платежей
    $('.payment-type-row .form-check').on('click', function(e) {
        // Проверяем, что клик был не на самом чекбоксе и не на лейбле (т.к. лейбл уже связан с чекбоксом)
        if (!$(e.target).is('input[type="checkbox"], label')) {
            var checkbox = $(this).find('input[type="checkbox"]');
            checkbox.prop('checked', !checkbox.prop('checked')).trigger('change');
        }
    });
    
    // Делаем весь контейнер кликабельным для чекбоксов методов оплаты
    $('.payment-methods-container .form-check').on('click', function(e) {
        // Проверяем, что клик был не на самом чекбоксе и не на лейбле (т.к. лейбл уже связан с чекбоксом)
        if (!$(e.target).is('input[type="checkbox"], label')) {
            var checkbox = $(this).find('input[type="checkbox"]');
            var row = $(this).closest('tr');
            var typeCheckbox = row.find('.payment-type-checkbox');
            
            // Если тип платежа еще не выбран, выбираем его сначала
            if (!typeCheckbox.prop('checked')) {
                typeCheckbox.prop('checked', true).trigger('change');
                // После этого выбираем текущий метод оплаты
                checkbox.prop('checked', true).trigger('change');
            } else if (!checkbox.prop('disabled')) {
                // Если тип платежа уже выбран и чекбокс не отключен, переключаем его
                checkbox.prop('checked', !checkbox.prop('checked')).trigger('change');
            }
        }
    });
    
    // Payment type checkbox handler
    $('.payment-type-checkbox').on('change', function() {
        var row = $(this).closest('tr');
        var paymentMethodCheckboxes = row.find('.payment-method-checkbox');
        var amountInput = row.find('.amount-input');
        
        if ($(this).is(':checked')) {
            // Enable fields when checkbox is checked
            paymentMethodCheckboxes.prop('disabled', false);
            amountInput.prop('disabled', false);
            // At least one payment method should be required
            paymentMethodCheckboxes.each(function() {
                $(this).attr('data-required', 'true');
            });
            
            // Автоматически выбираем наличный метод оплаты (Cash), если ни один метод ещё не выбран
            if (!row.find('.payment-method-checkbox:checked').length) {
                var cashCheckbox = row.find('.payment-method-checkbox[value="<?= PaymentType::CASH ?>"]');
                if (cashCheckbox.length) {
                    cashCheckbox.prop('checked', true).trigger('change');
                }
            }
        } else {
            // Disable fields when checkbox is unchecked
            paymentMethodCheckboxes.prop('disabled', true);
            amountInput.prop('disabled', true);
            paymentMethodCheckboxes.prop('checked', false);
            paymentMethodCheckboxes.removeAttr('data-required');
            amountInput.val('');
            
            // Hide dynamic amounts
            row.find('.dynamic-amounts').hide().empty();
            row.find('.amount-input').show();
        }

        // Обновляем информацию о выбранных кассах
        updateCashboxesInfo();
        calculateTotal();
    });

    // Payment method checkbox handler
    $(document).on('change', '.payment-method-checkbox', function() {
         var row = $(this).closest('tr');
         var amountColumn = row.find('.amount-column');
         var defaultInput = amountColumn.find('.amount-input');
         var dynamicContainer = amountColumn.find('.dynamic-amounts');
         var cashCheckbox = row.find('.payment-method-checkbox[value="<?= PaymentType::CASH ?>"]');
         var cardCheckbox = row.find('.payment-method-checkbox[value="<?= PaymentType::PAYMENT_CARD ?>"]');

         var selectedMethods = [];
         row.find('.payment-method-checkbox:checked').each(function() {
             selectedMethods.push($(this).val());
         });

         // Кассы будут выбраны автоматически на сервере

         if (selectedMethods.length === 0) {
             defaultInput.show();
             dynamicContainer.hide().empty();
         } else if (selectedMethods.length === 1) {
             defaultInput.show();
             defaultInput.attr('name', 'payment_types[' + getTypeId(row) + '][amounts][' + selectedMethods[0] + ']');
             dynamicContainer.hide().empty();
         } else {
             defaultInput.hide();
             dynamicContainer.show().empty();

             selectedMethods.forEach(function(method) {
                 var methodName = method == <?= PaymentType::CASH ?> ? "<?= Yii::t('app', 'cash') ?>" : "<?= Yii::t('app', 'payment_card') ?>";
                 var input = $('<div class="mb-2"><label>' + methodName + '</label><input type="text" class="form-control payment-amount" name="payment_types[' + getTypeId(row) + '][amounts][' + method + ']" placeholder="' + methodName + '" inputmode="numeric" pattern="[0-9\\s]*"><div class="error-container"></div></div>');
                 dynamicContainer.append(input);
             });

             // Автоматически распределяем сумму между наличными и картой
             autoDistributeToMethods(row);
         }
     });
     
     function getTypeId(row) {
         return row.find('input[type="checkbox"]').first().val();
     }



     // Упрощенная функция автоматического распределения между методами оплаты
     function autoDistributeToMethods(row) {
         var typeId = row.data('type');
         var remainingAmount = 0;

         // Получаем оставшуюся сумму для зарплаты
         if (typeId == <?= WorkerFinances::TYPE_SALARY ?>) {
             var originalSalary = parseFloat($('#worker-salary').text().replace(/[^\d.-]/g, '')) || 0;
             var originalTotalPaid = parseFloat($('#worker-total-paid').text().replace(/[^\d.-]/g, '')) || 0;
             remainingAmount = Math.max(0, originalSalary - originalTotalPaid);
         }

         if (remainingAmount > 0) {
             var dynamicInputs = row.find('.dynamic-amounts input');
             if (dynamicInputs.length === 2) {
                 // Если есть два поля (наличные и карта), распределяем остаток на карту
                 var cashInput = dynamicInputs.filter('[name*="[<?= PaymentType::CASH ?>]"]');
                 var cardInput = dynamicInputs.filter('[name*="[<?= PaymentType::PAYMENT_CARD ?>]"]');

                 if (cashInput.length && cardInput.length) {
                     // Оставляем поле наличных пустым, весь остаток на карту
                     cashInput.val('');
                     cardInput.val(formatNumberInput(remainingAmount.toString()));
                 }
             }
         }
     }

    // Debt payment checkbox handler
    $('#debt-payment-checkbox').on('change', function() {
        var details = $('#debt-payment-details');
        var paymentMethod = $('#debt-payment-method');
        var amountInput = $('#debt-payment-amount');
        
        if ($(this).is(':checked')) {
            details.show();
            paymentMethod.prop('disabled', false);
            amountInput.prop('disabled', false);
            paymentMethod.prop('required', true);
            amountInput.prop('required', true);
        } else {
            details.hide();
            paymentMethod.prop('disabled', true);
            amountInput.prop('disabled', true);
            paymentMethod.prop('required', false);
            amountInput.prop('required', false);
            paymentMethod.val('');
            amountInput.val('');
        }
        
        calculateTotal();
    });

    // Amount input handler
    $(document).on('input', '.payment-amount', function() {
        calculateTotal();
    });

    // Worker selection handler
    $('#worker_id').on('change', function() {
        var workerId = $(this).val();
        var month = $('#month').val();
        
        if (workerId && month) {
            loadWorkerInfo(workerId, month);
        } else {
            $('#worker-info').hide();
        }
    });

    // Month change handler
    $('#month').on('change', function() {
        var workerId = $('#worker_id').val();
        var month = $(this).val();
        
        if (workerId && month) {
            loadWorkerInfo(workerId, month);
        }
    });

    function loadWorkerInfo(workerId, month) {
        $.ajax({
            url: '/backend/worker-payment/get-worker-info',
            type: 'GET',
            data: {
                worker_id: workerId,
                month: month
            },
            success: function(response) {
                if (response.success) {
                    $('#worker-salary').text(formatNumber(response.salary));
                    
                    // Вычисляем общую сумму выплат только по зарплате и авансу (исключаем долги и другие выплаты)
                    var salaryPaid = response.payments[<?= WorkerFinances::TYPE_SALARY ?>] || 0;
                    var advancePaid = response.payments[<?= WorkerFinances::TYPE_ADVANCE ?>] || 0;
                    var totalSalaryAndAdvancePaid = salaryPaid + advancePaid;
                    
                    $('#worker-total-paid').text(formatNumber(totalSalaryAndAdvancePaid));
                    $('#worker-info').show();
                    
                    // Show/hide debt section based on debt amount
                    if (response.debt && response.debt > 0) {
                        $('#worker-debt-amount').text(formatNumber(response.debt));
                        $('#debt-payment-section').show();
                        
                        // Show remaining salary info (будет пересчитано позже)
                        $('#remaining-salary-info').show();
                    } else {
                        $('#debt-payment-section').hide();
                        $('#debt-payment-checkbox').prop('checked', false).trigger('change');
                        
                        // Показываем оставшуюся зарплату, если есть что показать
                        var remainingSalary = response.salary - totalSalaryAndAdvancePaid;
                        
                        if (remainingSalary > 0) {
                            $('#worker-remaining-salary').text(formatNumber(remainingSalary));
                            $('#remaining-salary-info').show();
                        } else {
                            $('#remaining-salary-info').hide();
                        }
                    }
                    
                    // Update payment amounts based on existing payments
                    updatePaymentAmounts(response.payments, response.salary);
                } else {
                    alert(response.message);
                }
            },
            error: function() {
                alert('Ошибка при загрузке информации о работнике');
            }
        });
    }

    function updatePaymentAmounts(payments, salary) {
        // Reset all amounts and uncheck all checkboxes
        $('.payment-type-checkbox').prop('checked', false).trigger('change');

        // Set salary amount if not fully paid
        var salaryPaid = payments[<?= WorkerFinances::TYPE_SALARY ?>] || 0;
        var advancePaid = payments[<?= WorkerFinances::TYPE_ADVANCE ?>] || 0;
        var totalSalaryAndAdvancePaid = salaryPaid + advancePaid;
        var remainingSalary = salary - totalSalaryAndAdvancePaid;

        if (remainingSalary > 0) {
            var salaryCheckbox = $('#payment_type_<?= WorkerFinances::TYPE_SALARY ?>');
            salaryCheckbox.prop('checked', true).trigger('change');

            // Set default payment method and amount
            var salaryRow = salaryCheckbox.closest('tr');
            salaryRow.find('.payment-method-checkbox[value="<?= PaymentType::CASH ?>"]').prop('checked', true).trigger('change'); // Default to cash
            salaryRow.find('.amount-input').val(formatNumberInput(remainingSalary.toString()));

            // НЕ активируем автоматически погашение долга - пользователь сам решает
            var debt = parseFloat($('#worker-debt-amount').text().replace(/[^\d.-]/g, '')) || 0;
            if (debt > 0 && $('#debt-payment-section').is(':visible')) {
                // Только показываем предупреждение, но не активируем автоматически
                showDebtPaymentWarning();
            }
        }

        calculateTotal();
    }

    function calculateTotal() {
        var total = 0;
        var hasSalaryOrAdvance = false;
        
        // Calculate total from table rows (все выплаты)
        $('.payment-amount:not(:disabled)').each(function() {
            var amount = getNumericValue($(this).val());
            total += amount;
            
            // Проверяем, есть ли выплата зарплаты или аванса
            var row = $(this).closest('tr');
            var typeId = row.data('type');
            if (typeId == <?= WorkerFinances::TYPE_SALARY ?> || typeId == <?= WorkerFinances::TYPE_ADVANCE ?>) {
                hasSalaryOrAdvance = true;
            }
        });
        
        // Пересчитываем оставшуюся зарплату
        updateRemainingSalary();
    }

    function updateRemainingSalary() {
        // Получаем изначальную зарплату
        var originalSalary = parseFloat($('#worker-salary').text().replace(/[^\d.-]/g, '')) || 0;
        
        // Считаем сумму выплат по зарплате и авансу (только текущие выплаты в форме)
        var currentSalaryAndAdvancePayments = 0;
        $('.payment-amount:not(:disabled)').each(function() {
            var amount = getNumericValue($(this).val());
            var row = $(this).closest('tr');
            var typeId = row.data('type');
            
            // Учитываем только зарплату и аванс
            if (typeId == <?= WorkerFinances::TYPE_SALARY ?> || typeId == <?= WorkerFinances::TYPE_ADVANCE ?>) {
                currentSalaryAndAdvancePayments += amount;
            }
        });
        
        // Получаем уже выплаченную сумму (только зарплата и аванс, без текущих выплат)
        var originalTotalPaid = parseFloat($('#worker-total-paid').text().replace(/[^\d.-]/g, '')) || 0;
        
        // Общая выплаченная сумма = уже выплаченное + текущие выплаты зарплаты/аванса
        var totalPaidIncludingCurrent = originalTotalPaid + currentSalaryAndAdvancePayments;
        
        // Оставшаяся зарплата = изначальная зарплата - общая выплаченная сумма
        var remainingSalary = originalSalary - totalPaidIncludingCurrent;
        
        // Обновляем отображение только если есть информация о зарплате
        if ($('#remaining-salary-info').is(':visible')) {
            $('#worker-remaining-salary').text(formatNumber(Math.max(0, remainingSalary)));
        }
    }



    function formatNumber(num) {
        return parseFloat(num || 0).toLocaleString('ru-RU');
    }

    // Функция для форматирования ввода чисел с разделителями тысяч
    function formatNumberInput(value) {
        // Если значение пустое или undefined
        if (!value || value === '') return '';
        
        // Убираем все символы кроме цифр
        var numericValue = value.toString().replace(/[^\d]/g, '');
        
        if (numericValue === '') return '';
        
        // Форматируем с разделителями тысяч
        return parseInt(numericValue).toLocaleString('ru-RU');
    }

    // Функция для получения чистого числового значения из отформатированного поля
    function getNumericValue(formattedValue) {
        if (!formattedValue || formattedValue === '') return 0;
        var numericString = formattedValue.toString().replace(/[^\d]/g, '');
        return parseInt(numericString || '0');
    }

    // Валидация ввода - разрешаем только цифры и пробелы
    $(document).on('keypress', 'input[inputmode="numeric"]', function(e) {
        var char = String.fromCharCode(e.which);
        if (!/[\d\s]/.test(char) && e.which !== 8 && e.which !== 0) {
            e.preventDefault();
        }
    });

    // Предотвращаем вставку недопустимых символов
    $(document).on('paste', 'input[inputmode="numeric"]', function(e) {
        var paste = (e.originalEvent.clipboardData || window.clipboardData).getData('text');
        if (!/^[\d\s]*$/.test(paste)) {
            e.preventDefault();
        }
    });

    // Упрощенная функция для автоматического распределения остатка
    function simpleAutoDistribute(changedInput) {
        var paymentRow = changedInput.closest('tr');
        var paymentTypeId = paymentRow.data('type');

        // Работаем только с зарплатой
        if (paymentTypeId != <?= WorkerFinances::TYPE_SALARY ?>) return;

        var dynamicInputs = paymentRow.find('.dynamic-amounts input');
        if (dynamicInputs.length !== 2) return; // Должно быть ровно 2 поля

        var cashInput = dynamicInputs.filter('[name*="[<?= PaymentType::CASH ?>]"]');
        var cardInput = dynamicInputs.filter('[name*="[<?= PaymentType::PAYMENT_CARD ?>]"]');

        if (cashInput.length && cardInput.length) {
            var originalSalary = parseFloat($('#worker-salary').text().replace(/[^\d.-]/g, '')) || 0;
            var originalTotalPaid = parseFloat($('#worker-total-paid').text().replace(/[^\d.-]/g, '')) || 0;
            var remainingAmount = Math.max(0, originalSalary - originalTotalPaid);

            var cashAmount = getNumericValue(cashInput.val());
            var cardAmount = getNumericValue(cardInput.val());

            // Если изменили поле наличных, пересчитываем карту
            if (changedInput.is(cashInput)) {
                var newCardAmount = Math.max(0, remainingAmount - cashAmount);
                cardInput.val(newCardAmount > 0 ? formatNumberInput(newCardAmount.toString()) : '');
            }
            // Если изменили поле карты, пересчитываем наличные
            else if (changedInput.is(cardInput)) {
                var newCashAmount = Math.max(0, remainingAmount - cardAmount);
                cashInput.val(newCashAmount > 0 ? formatNumberInput(newCashAmount.toString()) : '');
            }
        }
    }

    // Применяем форматирование ко всем полям ввода чисел
    $(document).on('input', 'input[inputmode="numeric"]', function() {
        var cursorPosition = this.selectionStart;
        var oldValue = $(this).val();
        var newValue = formatNumberInput(oldValue);

        $(this).val(newValue);

        // Восстанавливаем позицию курсора с учетом изменения длины
        var lengthDiff = newValue.length - oldValue.length;
        try {
            this.setSelectionRange(cursorPosition + lengthDiff, cursorPosition + lengthDiff);
        } catch (e) {
            // Fallback для случаев, когда setSelectionRange не поддерживается
            this.setSelectionRange(newValue.length, newValue.length);
        }

        // Если это поле в динамических суммах - запускаем упрощенное автораспределение
        if ($(this).closest('.dynamic-amounts').length > 0) {
            simpleAutoDistribute($(this));
        }

        // Пересчитываем общую сумму
        calculateTotal();
    });

    // Преобразуем отформатированные значения обратно в числа перед отправкой формы
    $('form').on('submit', function() {
        $('input[inputmode="numeric"]').each(function() {
            var numericValue = getNumericValue($(this).val());
            $(this).val(numericValue);
        });
    });

    // Clear errors when form fields change
    $('input, select').on('change', function() {
        var fieldId = $(this).attr('id');
        if (fieldId) {
            $('#' + fieldId + '-error').text('');
        }
    });

    // ========== VALIDATION FUNCTIONS ==========

    /**
     * Валидация максимальной суммы платежа
     */
    function validateMaxPaymentAmount() {
        var errors = [];
        var originalSalary = parseFloat($('#worker-salary').text().replace(/[^\d.-]/g, '')) || 0;
        var originalTotalPaid = parseFloat($('#worker-total-paid').text().replace(/[^\d.-]/g, '')) || 0;
        var maxAllowedAmount = Math.max(0, originalSalary - originalTotalPaid);

        // Проверяем каждое поле ввода суммы
        $('.payment-amount:not(:disabled)').each(function() {
            var amount = getNumericValue($(this).val());
            var row = $(this).closest('tr');
            var typeId = row.data('type');

            // Для зарплаты и аванса проверяем лимит
            if ((typeId == <?= WorkerFinances::TYPE_SALARY ?> || typeId == <?= WorkerFinances::TYPE_ADVANCE ?>) && amount > maxAllowedAmount) {
                errors.push({
                    field: $(this),
                    message: 'Сумма не может превышать оставшуюся зарплату: ' + formatNumber(maxAllowedAmount)
                });
            }

            // Проверяем минимальную сумму
            if (amount <= 0 && $(this).val() !== '') {
                errors.push({
                    field: $(this),
                    message: 'Сумма должна быть больше 0'
                });
            }
        });

        return errors;
    }

    /**
     * Валидация обязательного выбора типов оплаты
     */
    function validatePaymentTypesSelection() {
        var errors = [];
        var hasSelectedPaymentType = false;

        // Проверяем, выбран ли хотя бы один тип платежа
        $('.payment-type-checkbox:checked').each(function() {
            var row = $(this).closest('tr');
            var hasSelectedMethod = row.find('.payment-method-checkbox:checked').length > 0;
            var hasAmount = getNumericValue(row.find('.payment-amount').val()) > 0;

            if (hasSelectedMethod && hasAmount) {
                hasSelectedPaymentType = true;
            }
        });

        if (!hasSelectedPaymentType) {
            errors.push({
                field: $('.payment-type-checkbox').first(),
                message: 'Необходимо выбрать хотя бы один тип оплаты с суммой'
            });
        }

        return errors;
    }

    /**
     * Валидация сумм по типам платежей
     */
    function validatePaymentAmountsByType() {
        var errors = [];

        $('.payment-type-checkbox:checked').each(function() {
            var row = $(this).closest('tr');
            var selectedMethods = row.find('.payment-method-checkbox:checked');
            var dynamicAmounts = row.find('.dynamic-amounts input');

            if (selectedMethods.length > 1 && dynamicAmounts.length > 1) {
                // Если выбрано несколько методов, проверяем сумму каждого
                var totalMethodAmount = 0;
                var hasEmptyAmount = false;

                dynamicAmounts.each(function() {
                    var amount = getNumericValue($(this).val());
                    if (amount <= 0) {
                        hasEmptyAmount = true;
                        errors.push({
                            field: $(this),
                            message: 'Укажите сумму для выбранного метода оплаты'
                        });
                    }
                    totalMethodAmount += amount;
                });

                // Проверяем, что общая сумма методов равна сумме типа платежа
                var mainAmount = getNumericValue(row.find('.amount-input').val());
                if (!hasEmptyAmount && totalMethodAmount !== mainAmount && mainAmount > 0) {
                    errors.push({
                        field: dynamicAmounts.first(),
                        message: 'Сумма методов оплаты должна равняться общей сумме'
                    });
                }
            }
        });

        return errors;
    }

    /**
     * Валидация выбора кассы (автоматическая проверка)
     * Кассы выбираются автоматически на сервере, поэтому клиентская проверка не нужна
     */
    function validateCashboxSelection() {
        // Кассы выбираются автоматически на сервере, возвращаем пустой массив ошибок
        return [];
    }

    /**
     * Получить доступную кассу для типа платежа
     */
    function getAvailableCashboxForPaymentType(paymentType) {
        // Основываясь на структуре касс:
        // Наличные (type=1): Оля касса (id=3, is_main=2) - приоритет для выплат работникам
        // Безналичные (type=2,3,4): Электронная касса (id=2, is_main=2)

        if (paymentType == <?= PaymentType::CASH ?>) {
            return { id: 3, title: 'Оля касса' }; // Приоритет для наличных выплат работникам
        } else if (paymentType == <?= PaymentType::TRANSFER ?> ||
                   paymentType == <?= PaymentType::TERMINAL ?> ||
                   paymentType == <?= PaymentType::PAYMENT_CARD ?>) {
            return { id: 2, title: 'Электронная касса' };
        }

        return null;
    }

    /**
     * Валидация взыскания долга
     */
    function validateDebtPayment() {
        var errors = [];
        var debt = parseFloat($('#worker-debt-amount').text().replace(/[^\d.-]/g, '')) || 0;

        if (debt > 0) {
            var hasSalaryOrAdvancePayment = false;
            var totalSalaryAndAdvanceAmount = 0;

            // Проверяем, есть ли выплата зарплаты или аванса
            $('.payment-type-checkbox:checked').each(function() {
                var row = $(this).closest('tr');
                var typeId = row.data('type');
                var amount = getNumericValue(row.find('.payment-amount').val());

                if (typeId == <?= WorkerFinances::TYPE_SALARY ?> || typeId == <?= WorkerFinances::TYPE_ADVANCE ?>) {
                    hasSalaryOrAdvancePayment = true;
                    totalSalaryAndAdvanceAmount += amount;
                }
            });

            // Долг взыскивается только если пользователь выбрал это

                // Автоматически устанавливаем сумму взыскания долга
                if ($('#debt-payment-checkbox').is(':checked')) {
                    var debtPaymentAmount = Math.min(debt, totalSalaryAndAdvanceAmount);
                    $('#debt-payment-amount').val(formatNumberInput(debtPaymentAmount.toString()));
                }

                // Пользователь сам решает, взыскивать ли долг
            }
        }

        return errors;
    }

    /**
     * Показать ошибки валидации
     */
    function showValidationErrors(errors) {
        // Очищаем предыдущие ошибки
        $('.error-container').text('');
        $('.form-control').removeClass('is-invalid');

        errors.forEach(function(error) {
            error.field.addClass('is-invalid');

            // Находим контейнер для ошибки
            var errorContainer = error.field.closest('.form-group, .col-md-12, td').find('.error-container');
            if (errorContainer.length === 0) {
                // Создаем контейнер для ошибки, если его нет
                errorContainer = $('<div class="error-container text-danger small mt-1"></div>');
                error.field.after(errorContainer);
            }

            errorContainer.text(error.message);
        });
    }

    /**
     * Основная функция валидации формы
     */
    function validateForm() {
        var allErrors = [];

        // Запускаем все проверки
        allErrors = allErrors.concat(validateMaxPaymentAmount());
        allErrors = allErrors.concat(validatePaymentTypesSelection());
        allErrors = allErrors.concat(validatePaymentAmountsByType());
        allErrors = allErrors.concat(validateCashboxSelection());
        allErrors = allErrors.concat(validateDebtPayment());

        // Показываем ошибки
        showValidationErrors(allErrors);

        return allErrors.length === 0;
    }

    /**
     * Показать информацию о долге
     */
    function showDebtPaymentWarning() {
        var debt = parseFloat($('#worker-debt-amount').text().replace(/[^\d.-]/g, '')) || 0;
        if (debt > 0) {
            var warningHtml = '<div class="alert alert-info mt-2" id="debt-warning">' +
                '<i class="fas fa-info-circle"></i> ' +
                'У работника есть долг в размере ' + formatNumber(debt) + '. ' +
                'Вы можете выбрать погашение долга при необходимости.' +
                '</div>';

            // Удаляем предыдущее предупреждение, если есть
            $('#debt-warning').remove();

            // Добавляем предупреждение после секции долга
            $('#debt-payment-section').after(warningHtml);
        }
    }

    // Привязываем валидацию к событиям
    $(document).on('input change', '.payment-amount, .payment-type-checkbox, .payment-method-checkbox', function() {
        // Задержка для избежания частых вызовов
        clearTimeout(window.validationTimeout);
        window.validationTimeout = setTimeout(function() {
            validateForm();
        }, 300);
    });

    // Валидация при отправке формы
    $('#worker-payment-create-form').on('submit', function(e) {
        e.preventDefault();

        if (!validateForm()) {
            return false;
        }

        // Подготавливаем данные для отправки
        var formData = prepareFormData();

        // Отправляем данные на сервер
        submitPayment(formData);
    });

    /**
     * Подготовка данных формы для отправки
     */
    function prepareFormData() {
        var data = {
            worker_id: $('#worker_id').val(),
            month: $('#month').val(),
            payment_types: {}
        };

        // Собираем данные по типам платежей
        $('.payment-type-checkbox:checked').each(function() {
            var row = $(this).closest('tr');
            var typeId = row.data('type');
            var selectedMethods = row.find('.payment-method-checkbox:checked');
            var dynamicAmounts = row.find('.dynamic-amounts input');

            if (selectedMethods.length === 1) {
                // Один метод оплаты
                var amount = getNumericValue(row.find('.amount-input').val());
                if (amount > 0) {
                    data.payment_types[typeId] = {
                        amounts: {}
                    };
                    data.payment_types[typeId].amounts[selectedMethods.val()] = amount;
                }
            } else if (selectedMethods.length > 1 && dynamicAmounts.length > 0) {
                // Несколько методов оплаты
                data.payment_types[typeId] = {
                    amounts: {}
                };

                dynamicAmounts.each(function() {
                    var amount = getNumericValue($(this).val());
                    var methodType = $(this).attr('name').match(/\[(\d+)\]$/)[1];
                    if (amount > 0) {
                        data.payment_types[typeId].amounts[methodType] = amount;
                    }
                });
            }
        });

        // Добавляем данные о погашении долга
        if ($('#debt-payment-checkbox').is(':checked')) {
            var debtAmount = getNumericValue($('#debt-payment-amount').val());
            if (debtAmount > 0) {
                data.payment_types[<?= WorkerFinances::TYPE_DEBT_PAYMENT ?>] = {
                    amounts: {
                        <?= PaymentType::CASH ?>: debtAmount // Долг всегда погашается наличными
                    }
                };
            }
        }

        return data;
    }

    /**
     * Отправка данных на сервер
     */
    function submitPayment(data) {
        var submitBtn = $('#submit-payment-btn');
        var originalText = submitBtn.html();

        // Блокируем кнопку и показываем загрузку
        submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Обработка...');

        $.ajax({
            url: '/backend/worker-payment/store',
            type: 'POST',
            data: data,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // Успешное создание платежа
                    showSuccessMessage('Платеж успешно создан!');

                    // Проверяем, находимся ли мы в модалке
                    if ($('.modal').length > 0 && $('.modal').hasClass('show')) {
                        // Если в модалке, вызываем событие для обновления списка
                        $(document).trigger('paymentCreated');
                    } else {
                        // Если не в модалке, перенаправляем на страницу списка
                        setTimeout(function() {
                            window.location.href = '/backend/worker-payment';
                        }, 2000);
                    }
                } else {
                    // Ошибка валидации или создания
                    showErrorMessage(response.message || 'Ошибка при создании платежа');

                    // Показываем ошибки валидации
                    if (response.errors) {
                        showServerValidationErrors(response.errors);
                    }
                }
            },
            error: function(xhr, status, error) {
                showErrorMessage('Ошибка сервера: ' + error);
            },
            complete: function() {
                // Разблокируем кнопку
                submitBtn.prop('disabled', false).html(originalText);
            }
        });
    }

    /**
     * Показать сообщение об успехе
     */
    function showSuccessMessage(message) {
        var alert = $('<div class="alert alert-success alert-dismissible fade show" role="alert">' +
            '<i class="fas fa-check-circle"></i> ' + message +
            '<button type="button" class="close" data-dismiss="alert">' +
            '<span>&times;</span></button></div>');

        $('.worker-payment-form').prepend(alert);

        // Автоматически скрываем через 5 секунд
        setTimeout(function() {
            alert.alert('close');
        }, 5000);
    }

    /**
     * Показать сообщение об ошибке
     */
    function showErrorMessage(message) {
        var alert = $('<div class="alert alert-danger alert-dismissible fade show" role="alert">' +
            '<i class="fas fa-exclamation-circle"></i> ' + message +
            '<button type="button" class="close" data-dismiss="alert">' +
            '<span>&times;</span></button></div>');

        $('.worker-payment-form').prepend(alert);

        // Автоматически скрываем через 10 секунд
        setTimeout(function() {
            alert.alert('close');
        }, 10000);
    }

    /**
     * Показать ошибки серверной валидации
     */
    function showServerValidationErrors(errors) {
        for (var field in errors) {
            var errorMessage = errors[field];
            var fieldElement = null;

            // Находим соответствующее поле
            if (field === 'worker_id') {
                fieldElement = $('#worker_id');
            } else if (field === 'month') {
                fieldElement = $('#month');
            } else if (field === 'payment_amount_limit' || field === 'amount') {
                fieldElement = $('.payment-amount:not(:disabled)').first();
            } else if (field === 'debt_payment' || field === 'debt_full_payment') {
                fieldElement = $('#debt-payment-amount');
            }

            if (fieldElement) {
                fieldElement.addClass('is-invalid');
                var errorContainer = fieldElement.closest('.form-group, .col-md-12, td').find('.error-container');
                if (errorContainer.length === 0) {
                    errorContainer = $('<div class="error-container text-danger small mt-1"></div>');
                    fieldElement.after(errorContainer);
                }
                errorContainer.text(errorMessage);
            }
        }
    }
});
</script>
