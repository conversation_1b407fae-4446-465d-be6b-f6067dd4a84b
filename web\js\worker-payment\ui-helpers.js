/**
 * Модуль UI помощников для формы создания платежа работнику
 * Содержит функции для отображения сообщений и управления интерфейсом
 */

// Глобальный объект для UI функций
window.WorkerPaymentUI = {
    
    /**
     * Показать информацию о долге
     */
    showDebtPaymentWarning: function() {
        var debt = parseFloat($('#worker-debt-amount').text().replace(/[^\d.-]/g, '')) || 0;
        if (debt > 0) {
            var warningHtml = '<div class="alert alert-info mt-2" id="debt-warning">' +
                '<i class="fas fa-info-circle"></i> ' +
                'У работника есть долг в размере ' + window.WorkerPaymentCalculations.formatNumber(debt) + '. ' +
                'Вы можете выбрать погашение долга при необходимости.' +
                '</div>';
            
            // Удаляем предыдущее предупреждение, если есть
            $('#debt-warning').remove();
            
            // Добавляем предупреждение после секции долга
            $('#debt-payment-section').after(warningHtml);
        }
    },
    
    /**
     * Показать сообщение об успехе
     */
    showSuccessMessage: function(message) {
        var alert = $('<div class="alert alert-success alert-dismissible fade show" role="alert">' +
            '<i class="fas fa-check-circle"></i> ' + message +
            '<button type="button" class="close" data-dismiss="alert">' +
            '<span>&times;</span></button></div>');
        
        $('.worker-payment-form').prepend(alert);
        
        // Автоматически скрываем через 5 секунд
        setTimeout(function() {
            alert.alert('close');
        }, 5000);
    },
    
    /**
     * Показать сообщение об ошибке
     */
    showErrorMessage: function(message) {
        var alert = $('<div class="alert alert-danger alert-dismissible fade show" role="alert">' +
            '<i class="fas fa-exclamation-circle"></i> ' + message +
            '<button type="button" class="close" data-dismiss="alert">' +
            '<span>&times;</span></button></div>');
        
        $('.worker-payment-form').prepend(alert);
        
        // Автоматически скрываем через 10 секунд
        setTimeout(function() {
            alert.alert('close');
        }, 10000);
    },
    
    /**
     * Показать ошибки серверной валидации
     */
    showServerValidationErrors: function(errors) {
        for (var field in errors) {
            var errorMessage = errors[field];
            var fieldElement = null;
            
            // Находим соответствующее поле
            if (field === 'worker_id') {
                fieldElement = $('#worker_id');
            } else if (field === 'month') {
                fieldElement = $('#month');
            } else if (field === 'payment_amount_limit' || field === 'amount') {
                fieldElement = $('.payment-amount:not(:disabled)').first();
            } else if (field === 'debt_payment' || field === 'debt_full_payment') {
                fieldElement = $('#debt-payment-amount');
            }
            
            if (fieldElement) {
                fieldElement.addClass('is-invalid');
                var errorContainer = fieldElement.closest('.form-group, .col-md-12, td').find('.error-container');
                if (errorContainer.length === 0) {
                    errorContainer = $('<div class="error-container text-danger small mt-1"></div>');
                    fieldElement.after(errorContainer);
                }
                errorContainer.text(errorMessage);
            }
        }
    },
    
    /**
     * Очистить ошибки при изменении полей формы
     */
    clearFieldErrors: function() {
        $('input, select').on('change', function() {
            var fieldId = $(this).attr('id');
            if (fieldId) {
                $('#' + fieldId + '-error').text('');
            }
        });
    },
    
    /**
     * Получить доступную кассу для типа платежа
     */
    getAvailableCashboxForPaymentType: function(paymentType) {
        // Основываясь на структуре касс:
        // Наличные (type=1): Оля касса (id=3, is_main=2) - приоритет для выплат работникам
        // Безналичные (type=2,3,4): Электронная касса (id=2, is_main=2)
        
        if (paymentType == PAYMENT_TYPE_CASH) {
            return { id: 3, title: 'Оля касса' }; // Приоритет для наличных выплат работникам
        } else if (paymentType == PAYMENT_TYPE_TRANSFER || 
                   paymentType == PAYMENT_TYPE_TERMINAL || 
                   paymentType == PAYMENT_TYPE_PAYMENT_CARD) {
            return { id: 2, title: 'Электронная касса' };
        }
        
        return null;
    },
    
    /**
     * Получить ID типа из строки
     */
    getTypeId: function(row) {
        return row.find('input[type="checkbox"]').first().val();
    },
    
    /**
     * Инициализация обработчиков очистки ошибок
     */
    initErrorHandlers: function() {
        this.clearFieldErrors();
    }
};
