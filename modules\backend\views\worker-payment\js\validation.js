/**
 * Модуль валидации для формы создания платежа работнику
 * Содержит все функции валидации данных формы
 */

// Глобальный объект для валидации
window.WorkerPaymentValidation = {
    
    /**
     * Валидация максимальной суммы платежа
     */
    validateMaxPaymentAmount: function() {
        var errors = [];
        var originalSalary = parseFloat($('#worker-salary').text().replace(/[^\d.-]/g, '')) || 0;
        var originalTotalPaid = parseFloat($('#worker-total-paid').text().replace(/[^\d.-]/g, '')) || 0;
        var maxAllowedAmount = Math.max(0, originalSalary - originalTotalPaid);
        
        // Проверяем каждое поле ввода суммы
        $('.payment-amount:not(:disabled)').each(function() {
            var amount = window.WorkerPaymentCalculations.getNumericValue($(this).val());
            var row = $(this).closest('tr');
            var typeId = row.data('type');
            
            // Для зарплаты и аванса проверяем лимит
            if ((typeId == WORKER_FINANCES_TYPE_SALARY || typeId == WORKER_FINANCES_TYPE_ADVANCE) && amount > maxAllowedAmount) {
                errors.push({
                    field: $(this),
                    message: 'Сумма не может превышать оставшуюся зарплату: ' + window.WorkerPaymentCalculations.formatNumber(maxAllowedAmount)
                });
            }
            
            // Проверяем минимальную сумму
            if (amount <= 0 && $(this).val() !== '') {
                errors.push({
                    field: $(this),
                    message: 'Сумма должна быть больше 0'
                });
            }
        });
        
        return errors;
    },
    
    /**
     * Валидация обязательного выбора типов оплаты
     */
    validatePaymentTypesSelection: function() {
        var errors = [];
        var hasSelectedPaymentType = false;
        
        // Проверяем, выбран ли хотя бы один тип платежа
        $('.payment-type-checkbox:checked').each(function() {
            var row = $(this).closest('tr');
            var hasSelectedMethod = row.find('.payment-method-checkbox:checked').length > 0;
            var hasAmount = window.WorkerPaymentCalculations.getNumericValue(row.find('.payment-amount').val()) > 0;
            
            if (hasSelectedMethod && hasAmount) {
                hasSelectedPaymentType = true;
            }
        });
        
        if (!hasSelectedPaymentType) {
            errors.push({
                field: $('.payment-type-checkbox').first(),
                message: 'Необходимо выбрать хотя бы один тип оплаты с суммой'
            });
        }
        
        return errors;
    },
    
    /**
     * Валидация сумм по типам платежей
     */
    validatePaymentAmountsByType: function() {
        var errors = [];
        
        $('.payment-type-checkbox:checked').each(function() {
            var row = $(this).closest('tr');
            var selectedMethods = row.find('.payment-method-checkbox:checked');
            var dynamicAmounts = row.find('.dynamic-amounts input');
            
            if (selectedMethods.length > 1 && dynamicAmounts.length > 1) {
                // Если выбрано несколько методов, проверяем сумму каждого
                var totalMethodAmount = 0;
                var hasEmptyAmount = false;
                
                dynamicAmounts.each(function() {
                    var amount = window.WorkerPaymentCalculations.getNumericValue($(this).val());
                    if (amount <= 0) {
                        hasEmptyAmount = true;
                        errors.push({
                            field: $(this),
                            message: 'Укажите сумму для выбранного метода оплаты'
                        });
                    }
                    totalMethodAmount += amount;
                });
                
                // Проверяем, что общая сумма методов равна сумме типа платежа
                var mainAmount = window.WorkerPaymentCalculations.getNumericValue(row.find('.amount-input').val());
                if (!hasEmptyAmount && totalMethodAmount !== mainAmount && mainAmount > 0) {
                    errors.push({
                        field: dynamicAmounts.first(),
                        message: 'Сумма методов оплаты должна равняться общей сумме'
                    });
                }
            }
        });
        
        return errors;
    },
    
    /**
     * Валидация выбора кассы (автоматическая проверка)
     * Кассы выбираются автоматически на сервере, поэтому клиентская проверка не нужна
     */
    validateCashboxSelection: function() {
        // Кассы выбираются автоматически на сервере, возвращаем пустой массив ошибок
        return [];
    },
    
    /**
     * Валидация взыскания долга
     */
    validateDebtPayment: function() {
        var errors = [];
        var debt = parseFloat($('#worker-debt-amount').text().replace(/[^\d.-]/g, '')) || 0;
        
        if (debt > 0) {
            var hasSalaryOrAdvancePayment = false;
            var totalSalaryAndAdvanceAmount = 0;
            
            // Проверяем, есть ли выплата зарплаты или аванса
            $('.payment-type-checkbox:checked').each(function() {
                var row = $(this).closest('tr');
                var typeId = row.data('type');
                var amount = window.WorkerPaymentCalculations.getNumericValue(row.find('.payment-amount').val());
                
                if (typeId == WORKER_FINANCES_TYPE_SALARY || typeId == WORKER_FINANCES_TYPE_ADVANCE) {
                    hasSalaryOrAdvancePayment = true;
                    totalSalaryAndAdvanceAmount += amount;
                }
            });
            
            // Долг взыскивается только если пользователь выбрал это
            
            // Автоматически устанавливаем сумму взыскания долга
            if ($('#debt-payment-checkbox').is(':checked')) {
                var debtPaymentAmount = Math.min(debt, totalSalaryAndAdvanceAmount);
                $('#debt-payment-amount').val(window.WorkerPaymentCalculations.formatNumberInput(debtPaymentAmount.toString()));
            }
            
            // Пользователь сам решает, взыскивать ли долг
        }
        
        return errors;
    },
    
    /**
     * Показать ошибки валидации
     */
    showValidationErrors: function(errors) {
        // Очищаем предыдущие ошибки
        $('.error-container').text('');
        $('.form-control').removeClass('is-invalid');
        
        errors.forEach(function(error) {
            error.field.addClass('is-invalid');
            
            // Находим контейнер для ошибки
            var errorContainer = error.field.closest('.form-group, .col-md-12, td').find('.error-container');
            if (errorContainer.length === 0) {
                // Создаем контейнер для ошибки, если его нет
                errorContainer = $('<div class="error-container text-danger small mt-1"></div>');
                error.field.after(errorContainer);
            }
            
            errorContainer.text(error.message);
        });
    },
    
    /**
     * Основная функция валидации формы
     */
    validateForm: function() {
        var allErrors = [];
        
        // Запускаем все проверки
        allErrors = allErrors.concat(this.validateMaxPaymentAmount());
        allErrors = allErrors.concat(this.validatePaymentTypesSelection());
        allErrors = allErrors.concat(this.validatePaymentAmountsByType());
        allErrors = allErrors.concat(this.validateCashboxSelection());
        allErrors = allErrors.concat(this.validateDebtPayment());
        
        // Показываем ошибки
        this.showValidationErrors(allErrors);
        
        return allErrors.length === 0;
    }
};
