/**
 * Модуль обработчиков событий для формы создания платежа работнику
 * Содержит все обработчики событий формы
 */

// Глобальный объект для обработчиков событий
window.WorkerPaymentHandlers = {
    
    /**
     * Инициализация всех обработчиков событий
     */
    init: function() {
        this.initClickHandlers();
        this.initChangeHandlers();
        this.initInputHandlers();
        this.initFormSubmitHandler();
        this.initValidationHandlers();
    },
    
    /**
     * Обработчики кликов
     */
    initClickHandlers: function() {
        // Делаем весь контейнер кликабельным для чекбокса погашения долга
        $('#debt-payment-section .form-check').on('click', function(e) {
            // Проверяем, что клик был не на самом чекбоксе и не на лейбле (т.к. лейбл уже связан с чекбоксом)
            if (!$(e.target).is('input[type="checkbox"], label')) {
                var checkbox = $(this).find('input[type="checkbox"]');
                checkbox.prop('checked', !checkbox.prop('checked')).trigger('change');
            }
        });
        
        // Делаем весь контейнер кликабельным для чекбоксов типов платежей
        $('.payment-type-row .form-check').on('click', function(e) {
            // Проверяем, что клик был не на самом чекбоксе и не на лейбле (т.к. лейбл уже связан с чекбоксом)
            if (!$(e.target).is('input[type="checkbox"], label')) {
                var checkbox = $(this).find('input[type="checkbox"]');
                checkbox.prop('checked', !checkbox.prop('checked')).trigger('change');
            }
        });
        
        // Делаем весь контейнер кликабельным для чекбоксов методов оплаты
        $('.payment-methods-container .form-check').on('click', function(e) {
            // Проверяем, что клик был не на самом чекбоксе и не на лейбле (т.к. лейбл уже связан с чекбоксом)
            if (!$(e.target).is('input[type="checkbox"], label')) {
                var checkbox = $(this).find('input[type="checkbox"]');
                var row = $(this).closest('tr');
                var typeCheckbox = row.find('.payment-type-checkbox');
                
                // Если тип платежа еще не выбран, выбираем его сначала
                if (!typeCheckbox.prop('checked')) {
                    typeCheckbox.prop('checked', true).trigger('change');
                    // После этого выбираем текущий метод оплаты
                    checkbox.prop('checked', true).trigger('change');
                } else if (!checkbox.prop('disabled')) {
                    // Если тип платежа уже выбран и чекбокс не отключен, переключаем его
                    checkbox.prop('checked', !checkbox.prop('checked')).trigger('change');
                }
            }
        });
    },
    
    /**
     * Обработчики изменений
     */
    initChangeHandlers: function() {
        // Payment type checkbox handler
        $('.payment-type-checkbox').on('change', function() {
            var row = $(this).closest('tr');
            var paymentMethodCheckboxes = row.find('.payment-method-checkbox');
            var amountInput = row.find('.amount-input');
            
            if ($(this).is(':checked')) {
                // Enable fields when checkbox is checked
                paymentMethodCheckboxes.prop('disabled', false);
                amountInput.prop('disabled', false);
                // At least one payment method should be required
                paymentMethodCheckboxes.each(function() {
                    $(this).attr('data-required', 'true');
                });
                
                // Автоматически выбираем наличный метод оплаты (Cash), если ни один метод ещё не выбран
                if (!row.find('.payment-method-checkbox:checked').length) {
                    var cashCheckbox = row.find('.payment-method-checkbox[value="' + PAYMENT_TYPE_CASH + '"]');
                    if (cashCheckbox.length) {
                        cashCheckbox.prop('checked', true).trigger('change');
                    }
                }
            } else {
                // Disable fields when checkbox is unchecked
                paymentMethodCheckboxes.prop('disabled', true);
                amountInput.prop('disabled', true);
                paymentMethodCheckboxes.prop('checked', false);
                paymentMethodCheckboxes.removeAttr('data-required');
                amountInput.val('');
                
                // Hide dynamic amounts
                row.find('.dynamic-amounts').hide().empty();
                row.find('.amount-input').show();
            }
            
            window.WorkerPaymentCalculations.calculateTotal();
        });

        // Payment method checkbox handler
        $(document).on('change', '.payment-method-checkbox', function() {
             var row = $(this).closest('tr');
             var amountColumn = row.find('.amount-column');
             var defaultInput = amountColumn.find('.amount-input');
             var dynamicContainer = amountColumn.find('.dynamic-amounts');
             var cashCheckbox = row.find('.payment-method-checkbox[value="' + PAYMENT_TYPE_CASH + '"]');
             var cardCheckbox = row.find('.payment-method-checkbox[value="' + PAYMENT_TYPE_PAYMENT_CARD + '"]');

             var selectedMethods = [];
             row.find('.payment-method-checkbox:checked').each(function() {
                 selectedMethods.push($(this).val());
             });

             // Кассы будут выбраны автоматически на сервере

             if (selectedMethods.length === 0) {
                 defaultInput.show();
                 dynamicContainer.hide().empty();
             } else if (selectedMethods.length === 1) {
                 defaultInput.show();
                 defaultInput.attr('name', 'payment_types[' + window.WorkerPaymentUI.getTypeId(row) + '][amounts][' + selectedMethods[0] + ']');
                 dynamicContainer.hide().empty();
             } else {
                 defaultInput.hide();
                 dynamicContainer.show().empty();

                 selectedMethods.forEach(function(method) {
                     var methodName = method == PAYMENT_TYPE_CASH ? CASH_LABEL : PAYMENT_CARD_LABEL;
                     var input = $('<div class="mb-2"><label>' + methodName + '</label><input type="text" class="form-control payment-amount" name="payment_types[' + window.WorkerPaymentUI.getTypeId(row) + '][amounts][' + method + ']" placeholder="' + methodName + '" inputmode="numeric" pattern="[0-9\\s]*"><div class="error-container"></div></div>');
                     dynamicContainer.append(input);
                 });

                 // Автоматически распределяем сумму между наличными и картой
                 window.WorkerPaymentCalculations.autoDistributeToMethods(row);
             }
         });

        // Debt payment checkbox handler
        $('#debt-payment-checkbox').on('change', function() {
            var details = $('#debt-payment-details');
            var paymentMethod = $('#debt-payment-method');
            var amountInput = $('#debt-payment-amount');
            
            if ($(this).is(':checked')) {
                details.show();
                if (paymentMethod.length) paymentMethod.prop('disabled', false);
                amountInput.prop('disabled', false);
                if (paymentMethod.length) paymentMethod.prop('required', true);
                amountInput.prop('required', true);
            } else {
                details.hide();
                if (paymentMethod.length) paymentMethod.prop('disabled', true);
                amountInput.prop('disabled', true);
                if (paymentMethod.length) paymentMethod.prop('required', false);
                amountInput.prop('required', false);
                if (paymentMethod.length) paymentMethod.val('');
                amountInput.val('');
            }
            
            window.WorkerPaymentCalculations.calculateTotal();
        });

        // Worker selection handler
        $('#worker_id').on('change', function() {
            var workerId = $(this).val();
            var month = $('#month').val();
            
            if (workerId && month) {
                window.WorkerPaymentAjax.loadWorkerInfo(workerId, month);
            } else {
                $('#worker-info').hide();
            }
        });

        // Month change handler
        $('#month').on('change', function() {
            var workerId = $('#worker_id').val();
            var month = $(this).val();
            
            if (workerId && month) {
                window.WorkerPaymentAjax.loadWorkerInfo(workerId, month);
            }
        });
    },
    
    /**
     * Обработчики ввода
     */
    initInputHandlers: function() {
        // Amount input handler
        $(document).on('input', '.payment-amount', function() {
            window.WorkerPaymentCalculations.calculateTotal();
        });

        // Валидация ввода - разрешаем только цифры и пробелы
        $(document).on('keypress', 'input[inputmode="numeric"]', function(e) {
            var char = String.fromCharCode(e.which);
            if (!/[\d\s]/.test(char) && e.which !== 8 && e.which !== 0) {
                e.preventDefault();
            }
        });

        // Предотвращаем вставку недопустимых символов
        $(document).on('paste', 'input[inputmode="numeric"]', function(e) {
            var paste = (e.originalEvent.clipboardData || window.clipboardData).getData('text');
            if (!/^[\d\s]*$/.test(paste)) {
                e.preventDefault();
            }
        });

        // Применяем форматирование ко всем полям ввода чисел
        $(document).on('input', 'input[inputmode="numeric"]', function() {
            var cursorPosition = this.selectionStart;
            var oldValue = $(this).val();
            var newValue = window.WorkerPaymentCalculations.formatNumberInput(oldValue);

            $(this).val(newValue);

            // Восстанавливаем позицию курсора с учетом изменения длины
            var lengthDiff = newValue.length - oldValue.length;
            try {
                this.setSelectionRange(cursorPosition + lengthDiff, cursorPosition + lengthDiff);
            } catch (e) {
                // Fallback для случаев, когда setSelectionRange не поддерживается
                this.setSelectionRange(newValue.length, newValue.length);
            }

            // Если это поле в динамических суммах - запускаем упрощенное автораспределение
            if ($(this).closest('.dynamic-amounts').length > 0) {
                window.WorkerPaymentCalculations.simpleAutoDistribute($(this));
            }

            // Пересчитываем общую сумму
            window.WorkerPaymentCalculations.calculateTotal();
        });

        // Преобразуем отформатированные значения обратно в числа перед отправкой формы
        $('form').on('submit', function() {
            $('input[inputmode="numeric"]').each(function() {
                var numericValue = window.WorkerPaymentCalculations.getNumericValue($(this).val());
                $(this).val(numericValue);
            });
        });
    },
    
    /**
     * Обработчик отправки формы
     */
    initFormSubmitHandler: function() {
        // Валидация при отправке формы
        $('#worker-payment-create-form').on('submit', function(e) {
            e.preventDefault();
            
            if (!window.WorkerPaymentValidation.validateForm()) {
                return false;
            }
            
            // Подготавливаем данные для отправки
            var formData = window.WorkerPaymentAjax.prepareFormData();
            
            // Отправляем данные на сервер
            window.WorkerPaymentAjax.submitPayment(formData);
        });
    },
    
    /**
     * Обработчики валидации
     */
    initValidationHandlers: function() {
        // Привязываем валидацию к событиям
        $(document).on('input change', '.payment-amount, .payment-type-checkbox, .payment-method-checkbox', function() {
            // Задержка для избежания частых вызовов
            clearTimeout(window.validationTimeout);
            window.validationTimeout = setTimeout(function() {
                window.WorkerPaymentValidation.validateForm();
            }, 300);
        });
    }
};
