/**
 * Главный файл инициализации для формы создания платежа работнику
 * Координирует работу всех модулей и инициализирует приложение
 */

// Константы, которые будут переданы из PHP
var WORKER_FINANCES_TYPE_SALARY;
var WORKER_FINANCES_TYPE_ADVANCE;
var WORKER_FINANCES_TYPE_DEBT_PAYMENT;
var PAYMENT_TYPE_CASH;
var PAYMENT_TYPE_TRANSFER;
var PAYMENT_TYPE_TERMINAL;
var PAYMENT_TYPE_PAYMENT_CARD;
var CASH_LABEL;
var PAYMENT_CARD_LABEL;

// Главный объект приложения
window.WorkerPaymentApp = {
    
    /**
     * Инициализация приложения
     */
    init: function(constants) {
        // Устанавливаем константы
        this.setConstants(constants);
        
        // Инициализируем все модули
        this.initModules();
        
        // Инициализируем обработчики событий
        this.initEventHandlers();
        
        console.log('Worker Payment App initialized successfully');
    },
    
    /**
     * Установка констант из PHP
     */
    setConstants: function(constants) {
        WORKER_FINANCES_TYPE_SALARY = constants.WORKER_FINANCES_TYPE_SALARY;
        WORKER_FINANCES_TYPE_ADVANCE = constants.WORKER_FINANCES_TYPE_ADVANCE;
        WORKER_FINANCES_TYPE_DEBT_PAYMENT = constants.WORKER_FINANCES_TYPE_DEBT_PAYMENT;
        PAYMENT_TYPE_CASH = constants.PAYMENT_TYPE_CASH;
        PAYMENT_TYPE_TRANSFER = constants.PAYMENT_TYPE_TRANSFER;
        PAYMENT_TYPE_TERMINAL = constants.PAYMENT_TYPE_TERMINAL;
        PAYMENT_TYPE_PAYMENT_CARD = constants.PAYMENT_TYPE_PAYMENT_CARD;
        CASH_LABEL = constants.CASH_LABEL;
        PAYMENT_CARD_LABEL = constants.PAYMENT_CARD_LABEL;
    },
    
    /**
     * Инициализация всех модулей
     */
    initModules: function() {
        // Проверяем, что все модули загружены
        if (typeof window.WorkerPaymentValidation === 'undefined') {
            console.error('WorkerPaymentValidation module not loaded');
            return;
        }
        
        if (typeof window.WorkerPaymentCalculations === 'undefined') {
            console.error('WorkerPaymentCalculations module not loaded');
            return;
        }
        
        if (typeof window.WorkerPaymentUI === 'undefined') {
            console.error('WorkerPaymentUI module not loaded');
            return;
        }
        
        if (typeof window.WorkerPaymentAjax === 'undefined') {
            console.error('WorkerPaymentAjax module not loaded');
            return;
        }
        
        if (typeof window.WorkerPaymentHandlers === 'undefined') {
            console.error('WorkerPaymentHandlers module not loaded');
            return;
        }
        
        // Инициализируем UI помощники
        window.WorkerPaymentUI.initErrorHandlers();
        
        console.log('All modules loaded successfully');
    },
    
    /**
     * Инициализация обработчиков событий
     */
    initEventHandlers: function() {
        // Инициализируем обработчики событий формы
        window.WorkerPaymentHandlers.init();
        
        console.log('Event handlers initialized');
    },
    
    /**
     * Проверка готовности DOM
     */
    ready: function(constants) {
        var self = this;
        $(document).ready(function() {
            self.init(constants);
        });
    }
};
