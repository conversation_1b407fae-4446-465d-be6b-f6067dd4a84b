/**
 * Модуль расчетов для формы создания платежа работнику
 * Содержит функции для расчетов, форматирования и обработки числовых данных
 */

// Глобальный объект для расчетов
window.WorkerPaymentCalculations = {
    
    /**
     * Основная функция расчета общей суммы
     */
    calculateTotal: function() {
        var total = 0;
        var hasSalaryOrAdvance = false;
        
        // Calculate total from table rows (все выплаты)
        $('.payment-amount:not(:disabled)').each(function() {
            var amount = this.getNumericValue($(this).val());
            total += amount;
            
            // Проверяем, есть ли выплата зарплаты или аванса
            var row = $(this).closest('tr');
            var typeId = row.data('type');
            if (typeId == WORKER_FINANCES_TYPE_SALARY || typeId == WORKER_FINANCES_TYPE_ADVANCE) {
                hasSalaryOrAdvance = true;
            }
        }.bind(this));
        
        // Пересчитываем оставшуюся зарплату
        this.updateRemainingSalary();
    },
    
    /**
     * Обновление оставшейся зарплаты
     */
    updateRemainingSalary: function() {
        // Получаем изначальную зарплату
        var originalSalary = parseFloat($('#worker-salary').text().replace(/[^\d.-]/g, '')) || 0;
        
        // Считаем сумму выплат по зарплате и авансу (только текущие выплаты в форме)
        var currentSalaryAndAdvancePayments = 0;
        $('.payment-amount:not(:disabled)').each(function() {
            var amount = this.getNumericValue($(this).val());
            var row = $(this).closest('tr');
            var typeId = row.data('type');
            
            // Учитываем только зарплату и аванс
            if (typeId == WORKER_FINANCES_TYPE_SALARY || typeId == WORKER_FINANCES_TYPE_ADVANCE) {
                currentSalaryAndAdvancePayments += amount;
            }
        }.bind(this));
        
        // Получаем уже выплаченную сумму (только зарплата и аванс, без текущих выплат)
        var originalTotalPaid = parseFloat($('#worker-total-paid').text().replace(/[^\d.-]/g, '')) || 0;
        
        // Общая выплаченная сумма = уже выплаченное + текущие выплаты зарплаты/аванса
        var totalPaidIncludingCurrent = originalTotalPaid + currentSalaryAndAdvancePayments;
        
        // Оставшаяся зарплата = изначальная зарплата - общая выплаченная сумма
        var remainingSalary = originalSalary - totalPaidIncludingCurrent;
        
        // Обновляем отображение только если есть информация о зарплате
        if ($('#remaining-salary-info').is(':visible')) {
            $('#worker-remaining-salary').text(this.formatNumber(Math.max(0, remainingSalary)));
        }
    },
    
    /**
     * Форматирование числа для отображения
     */
    formatNumber: function(num) {
        return parseFloat(num || 0).toLocaleString('ru-RU');
    },
    
    /**
     * Функция для форматирования ввода чисел с разделителями тысяч
     */
    formatNumberInput: function(value) {
        // Если значение пустое или undefined
        if (!value || value === '') return '';
        
        // Убираем все символы кроме цифр
        var numericValue = value.toString().replace(/[^\d]/g, '');
        
        if (numericValue === '') return '';
        
        // Форматируем с разделителями тысяч
        return parseInt(numericValue).toLocaleString('ru-RU');
    },
    
    /**
     * Функция для получения чистого числового значения из отформатированного поля
     */
    getNumericValue: function(formattedValue) {
        if (!formattedValue || formattedValue === '') return 0;
        var numericString = formattedValue.toString().replace(/[^\d]/g, '');
        return parseInt(numericString || '0');
    },
    
    /**
     * Упрощенная функция автоматического распределения остатка
     */
    simpleAutoDistribute: function(changedInput) {
        var paymentRow = changedInput.closest('tr');
        var paymentTypeId = paymentRow.data('type');

        // Работаем только с зарплатой
        if (paymentTypeId != WORKER_FINANCES_TYPE_SALARY) return;

        var dynamicInputs = paymentRow.find('.dynamic-amounts input');
        if (dynamicInputs.length !== 2) return; // Должно быть ровно 2 поля

        var cashInput = dynamicInputs.filter('[name*="[' + PAYMENT_TYPE_CASH + ']"]');
        var cardInput = dynamicInputs.filter('[name*="[' + PAYMENT_TYPE_PAYMENT_CARD + ']"]');

        if (cashInput.length && cardInput.length) {
            var originalSalary = parseFloat($('#worker-salary').text().replace(/[^\d.-]/g, '')) || 0;
            var originalTotalPaid = parseFloat($('#worker-total-paid').text().replace(/[^\d.-]/g, '')) || 0;
            var remainingAmount = Math.max(0, originalSalary - originalTotalPaid);

            var cashAmount = this.getNumericValue(cashInput.val());
            var cardAmount = this.getNumericValue(cardInput.val());

            // Если изменили поле наличных, пересчитываем карту
            if (changedInput.is(cashInput)) {
                var newCardAmount = Math.max(0, remainingAmount - cashAmount);
                cardInput.val(newCardAmount > 0 ? this.formatNumberInput(newCardAmount.toString()) : '');
            }
            // Если изменили поле карты, пересчитываем наличные
            else if (changedInput.is(cardInput)) {
                var newCashAmount = Math.max(0, remainingAmount - cardAmount);
                cashInput.val(newCashAmount > 0 ? this.formatNumberInput(newCashAmount.toString()) : '');
            }
        }
    },
    
    /**
     * Упрощенная функция для автоматического распределения между методами оплаты
     */
    autoDistributeToMethods: function(row) {
        var typeId = row.data('type');
        var remainingAmount = 0;

        // Получаем оставшуюся сумму для зарплаты
        if (typeId == WORKER_FINANCES_TYPE_SALARY) {
            var originalSalary = parseFloat($('#worker-salary').text().replace(/[^\d.-]/g, '')) || 0;
            var originalTotalPaid = parseFloat($('#worker-total-paid').text().replace(/[^\d.-]/g, '')) || 0;
            remainingAmount = Math.max(0, originalSalary - originalTotalPaid);
        }

        if (remainingAmount > 0) {
            var dynamicInputs = row.find('.dynamic-amounts input');
            if (dynamicInputs.length === 2) {
                // Если есть два поля (наличные и карта), распределяем остаток на карту
                var cashInput = dynamicInputs.filter('[name*="[' + PAYMENT_TYPE_CASH + ']"]');
                var cardInput = dynamicInputs.filter('[name*="[' + PAYMENT_TYPE_PAYMENT_CARD + ']"]');

                if (cashInput.length && cardInput.length) {
                    // Оставляем поле наличных пустым, весь остаток на карту
                    cashInput.val('');
                    cardInput.val(this.formatNumberInput(remainingAmount.toString()));
                }
            }
        }
    },
    
    /**
     * Обновление сумм платежей на основе данных работника
     */
    updatePaymentAmounts: function(payments, salary) {
        // Reset all amounts and uncheck all checkboxes
        $('.payment-type-checkbox').prop('checked', false).trigger('change');
        
        // Set salary amount if not fully paid
        var salaryPaid = payments[WORKER_FINANCES_TYPE_SALARY] || 0;
        var advancePaid = payments[WORKER_FINANCES_TYPE_ADVANCE] || 0;
        var totalSalaryAndAdvancePaid = salaryPaid + advancePaid;
        var remainingSalary = salary - totalSalaryAndAdvancePaid;
        
        if (remainingSalary > 0) {
            var salaryCheckbox = $('#payment_type_' + WORKER_FINANCES_TYPE_SALARY);
            salaryCheckbox.prop('checked', true).trigger('change');
            
            // Set default payment method and amount
            var salaryRow = salaryCheckbox.closest('tr');
            salaryRow.find('.payment-method-checkbox[value="' + PAYMENT_TYPE_CASH + '"]').prop('checked', true).trigger('change'); // Default to cash
            salaryRow.find('.amount-input').val(this.formatNumberInput(remainingSalary.toString()));
            
            // НЕ активируем автоматически погашение долга - пользователь сам решает
            var debt = parseFloat($('#worker-debt-amount').text().replace(/[^\d.-]/g, '')) || 0;
            if (debt > 0 && $('#debt-payment-section').is(':visible')) {
                // Только показываем предупреждение, но не активируем автоматически
                window.WorkerPaymentUI.showDebtPaymentWarning();
            }
        }
        
        this.calculateTotal();
    }
};
